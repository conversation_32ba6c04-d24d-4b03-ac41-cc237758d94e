/* eslint-disable react/prop-types */
import {Link} from "react-router-dom";
import {regionsList} from "../../constant/regionList.js";
import {languageOptions} from "../../constant/languages.js";
import {genresList} from "../../constant/genresList.js";
import {useEffect, useState} from "react";
import {useDebounce} from "../../hooks/common/useDebounce.js";
import Spinner from "../../components/common/Spinner.jsx";
import {useCategories} from "../../hooks/categories/useCategories.js";

export default function ShopFilters({
                                           isOpen,
                                           onClose,
                                           selectedGenre,
                                           setSelectedGenre,
                                           selectedLanguage,
                                           setSelectedLanguage,
                                           selectedRegion,
                                           setSelectedRegion,
                                           setPrices,
                                           setSelectedBoxes,
                                           selectedBoxes,
                                       }) {

    const resetFilters = () => {
        setSelectedRegion("");
        setSelectedLanguage("");
        setSelectedGenre("");
        setPrices([]);
        setSelectedBoxes({})
    }

    const {categories, categoriesLoading} = useCategories();

    const [fPrices, setFPrices] = useState([]);

    const debouncedPriceTerm = useDebounce(fPrices, 500);
    useEffect(() => {
        setPrices(debouncedPriceTerm)
    }, [debouncedPriceTerm]);

    const handleCheckboxChange = (id, value) => {
        setSelectedBoxes((prev) => {
            const updatedMap = { ...prev };

            if (!updatedMap[id]) updatedMap[id] = [value];
            else {
                const index = updatedMap[id].indexOf(value);
                if (index > -1) {
                    updatedMap[id].splice(index, 1);
                    if (updatedMap[id].length === 0) delete updatedMap[id];
                }
                else updatedMap[id].push(value);
            }

            return updatedMap;
        });
    };


    const updateCategories = (id, value) => {
        setSelectedBoxes((prev) => {
            const updatedMap = { ...prev };
            updatedMap[id] = [value];
            return updatedMap;
        });
    }

    return (
        <>
            <div
                className={
                    "col col-lg-3 col-xl-2 cate_filter_con flex-column " +
                    (isOpen ? "d-flex" : "d-none d-lg-flex")
                }>
                <div className="col">
                    <p className="col d-none d-lg-flex cate_filter_tigger align-items-center gap-2 mb-3">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                            <g fill="none" fillRule="evenodd">
                                <path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
                                <path
                                    fill="currentColor"
                                    d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                                />
                            </g>
                        </svg>
                        Filter by
                        <span className="d-flex justify-content-center align-items-center ms-auto">
              {Object.values(selectedBoxes).flat().length}
            </span>
                    </p>

                    <p className="col d-flex d-lg-none cate_filter_title align-items-center gap-2 mb-4">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                            <g fill="none" fillRule="evenodd">
                                <path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
                                <path
                                    fill="currentColor"
                                    d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                                />
                            </g>
                        </svg>
                        Filter by
                        <span
                            onClick={onClose}
                            className="d-flex justify-content-center align-items-center ms-auto">
              <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24">
                <path
                    fill="currentColor"
                    d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"></path>
              </svg>
            </span>
                    </p>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Category</li>
                        {categories && !categoriesLoading ? <div className="col">
                            {categories.map(item=> <Link className="d-block mb-2" key={item._id} onClick={()=> updateCategories('category', item.slug)}>
                                <p className="cate_filter_text">{item.name}</p>
                            </Link>)}
                        </div> : <Spinner />}
                    </div>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Availability</li>
                        <div className="col">
                            <div className="col d-flex align-items-start gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check mt-1"
                                    type="checkbox"
                                    value="switzerland"
                                    id="region"
                                    checked={selectedBoxes['region']?.[0]}
                                    onChange={e=> handleCheckboxChange('region', e.target.value)}
                                />
                                <label
                                    className="cate_filter_label active"
                                    htmlFor="region">
                                    Can be activated in: <span>Switzerland</span>
                                </label>
                            </div>
                            <div className="col d-flex align-items-center gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check"
                                    type="checkbox"
                                    id="instantDelivery"
                                    checked={selectedBoxes['instantDelivery']?.[0]}
                                    onChange={e=> setSelectedBoxes(prev=> ({...prev, instantDelivery: [e.target.checked]}))}
                                />
                                <label className="cate_filter_label" htmlFor="instantDelivery">
                                    Only pre orders
                                </label>
                            </div>
                            <div className="col d-flex align-items-center gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check"
                                    type="checkbox"
                                    value="0"
                                    id="stock"
                                    checked={selectedBoxes['stock[gt]']}
                                    onChange={e=> handleCheckboxChange("stock[gt]", e.target.value)}
                                />
                                <label className="cate_filter_label" htmlFor="stock">
                                    Items in stock
                                </label>
                            </div>
                            <div className="col d-flex align-items-center gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check"
                                    type="checkbox"
                                    value=""
                                    id="defaultCheck2"
                                />
                                <label className="cate_filter_label" htmlFor="defaultCheck2">
                                    <svg
                                        className="me-1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="1em"
                                        height="1em"
                                        viewBox="0 0 256 256">
                                        <path
                                            fill="currentColor"
                                            d="M143.38 17.85a8 8 0 0 0-12.63 3.41l-22 60.41l-24.16-23.41a8 8 0 0 0-11.93.89C51 87.53 40 116.08 40 144a88 88 0 0 0 176 0c0-59.45-50.79-108-72.62-126.15m40.51 135.49a57.6 57.6 0 0 1-46.56 46.55a7.7 7.7 0 0 1-1.33.11a8 8 0 0 1-1.32-15.89c16.57-2.79 30.63-16.85 33.44-33.45a8 8 0 0 1 15.78 2.68Z"></path>
                                    </svg>
                                    Hot items
                                </label>
                            </div>
                        </div>
                    </div>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Type</li>
                        <div className="col">
                            <div className="col d-flex align-items-start gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check mt-1"
                                    type="checkbox"
                                    value=""
                                    id="defaultCheck1"
                                />
                                <label className="cate_filter_label" htmlFor="defaultCheck1">
                                    Codes/Keys
                                </label>
                            </div>
                            <div className="col d-flex align-items-center gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check"
                                    type="checkbox"
                                    value=""
                                    id="defaultCheck2"
                                />
                                <label className="cate_filter_label" htmlFor="defaultCheck2">
                                    Accounts
                                </label>
                            </div>
                        </div>
                    </div>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Delivery</li>
                        <div className="col">
                            <div className="col d-flex align-items-start gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check mt-1"
                                    type="checkbox"
                                    value="instant"
                                    id="instant"
                                    checked={selectedBoxes['delivery']?.includes('instant')}
                                    onChange={e=> handleCheckboxChange("delivery", e.target.value)}
                                />
                                <label className="cate_filter_label" htmlFor="defaultCheck1">
                                    Instant
                                </label>
                            </div>
                            <div className="col d-flex align-items-center gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check"
                                    type="checkbox"
                                    value="24"
                                    id="24"
                                    checked={selectedBoxes['delivery']?.includes('24')}
                                    onChange={e=> handleCheckboxChange("delivery", e.target.value)}
                                />
                                <label className="cate_filter_label" htmlFor="defaultCheck2">
                                    24H
                                </label>
                            </div>
                            <div className="col d-flex align-items-center gap-2 mb-2">
                                <input
                                    className="col-auto cate_filter_check"
                                    type="checkbox"
                                    value="48"
                                    id="48"
                                    checked={selectedBoxes['delivery']?.includes('48')}
                                    onChange={e=> handleCheckboxChange("delivery", e.target.value)}
                                />
                                <label className="cate_filter_label" htmlFor="defaultCheck2">
                                    48H
                                </label>
                            </div>
                        </div>
                    </div>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Price</li>
                        <div className="col d-flex gap-2">
                            <div className="col">
                                <input
                                    type="text"
                                    className="cate_filter_input"
                                    placeholder="Min: $0"
                                    value={fPrices[0] ?? ""}
                                    onChange={e=> setFPrices(prevState => ([e.target.value, prevState[1]]))}
                                />
                            </div>
                            <div className="col">
                                <input
                                    type="text"
                                    className="cate_filter_input"
                                    placeholder="Max: $1000"
                                    value={fPrices[1] ?? ""}
                                    onChange={e=> setFPrices(prevState => ([prevState[0], e.target.value]))}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Region</li>
                        <select className="cate_filter_sel" value={selectedRegion} onChange={e=> setSelectedRegion(e.target.value)}>
                            <option value="">Select</option>
                            {regionsList.map((item, index) => <option key={index} value={item._id}>{item.name}</option>)}
                        </select>
                    </div>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Language</li>
                        <select className="cate_filter_sel" value={selectedLanguage} onChange={e=> setSelectedLanguage(e.target.value)}>
                            <option value="">Select</option>
                            {languageOptions.map((item, index) => <option key={index} value={item._id}>{item.value}</option>)}
                        </select>
                    </div>

                    <div className="col mb-4">
                        <li className="cate_filter_head mb-2">Genre</li>
                        <select className="cate_filter_sel" value={selectedGenre} onChange={e=> setSelectedGenre(e.target.value)}>
                            <option value="">Select</option>
                            {genresList.map((item, index) => <option key={index} value={item._id}>{item.name}</option>)}
                        </select>
                    </div>
                </div>

                <div className="col-auto d-flex d-lg-none gap-2 cate_filter_btn_con sticky-bottom">
                    <button type="button" className="col cate_filter_btn dark" onClick={resetFilters}>
                        Clear all
                    </button>
                    <button type="button" className="col cate_filter_btn" onClick={onClose}>
                        Apply filter
                    </button>
                </div>
            </div>
        </>
    );
}
