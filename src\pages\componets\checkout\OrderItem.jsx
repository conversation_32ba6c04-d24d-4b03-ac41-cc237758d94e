import {useEffect, useState} from "react";
import {usePatchCart} from "../../../hooks/cart/usePatchCart.js";
import {useDeleteCart} from "../../../hooks/cart/useDeleteCart.js";
import Spinner from "../../../components/common/Spinner.jsx";

export default function OrderItem({offerName, region, sellerName, deliveryTime, offer, category, customerPays, cartId, numberOfItems}){

    const [quantityState, setQuantityState] = useState(numberOfItems);
    const [p1, p2] = customerPays.toString().split(".")

    const {patchLoading, patchRefetch} = usePatchCart({offerId: offer, quantity: quantityState, cartId});
    const {deleteLoading, deleteRefetch} = useDeleteCart({offerId: offer, quantity: quantityState, cartId});

    useEffect(() => {
        if(quantityState === numberOfItems) return;
        patchRefetch().finally()
    }, [quantityState]);

    return (
        <div className="col mb-4">
            <div className="col d-flex gap-3 align-items-center mb-2">
                <p className="cart_ord_title">
                    {offerName} {region}
                </p>

                {deleteLoading ? <Spinner /> :
                    <span className="cart_ord_del" onClick={() => deleteRefetch()}>
                                  <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="1em"
                                      height="1em"
                                      viewBox="0 0 20 20">
                                    <path
                                        fill="currentColor"
                                        d="m6 2l2-2h4l2 2h4v2H2V2zM3 6h14l-1 14H4zm5 2v10h1V8zm3 0v10h1V8z"
                                    />
                                  </svg>
                                </span>}
            </div>
            <div className="d-flex flex-wrap gap-2 mb-2">
                <p className="cart_item_dea">
                    Sold by:
                    <span>
                                    <u>{sellerName.name}</u>
                                  </span>
                </p>
                <p className="cart_item_dea">
                    Delivery: <span>{deliveryTime} {deliveryTime !== 'instant' && 'H'}</span>
                </p>
                <p className="cart_item_dea">
                    Type: <span>Digital key</span>
                </p>
                <p className="cart_item_dea">
                    Region: <span>{region}</span>
                </p>
                <p className="cart_item_dea">
                    Platform: <span>{category}</span>
                </p>
            </div>

            <div className="col d-flex justify-content-between align-items-center">
                <div className="cart_ord_count border-0 d-flex gap-3 align-items-center">
                                  <span onClick={() => quantityState > 1 && setQuantityState(quantityState - 1)}>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="1em"
                                        height="1em"
                                        viewBox="0 0 24 24">
                                      <path
                                          fill="currentColor"
                                          d="M19 12.998H5v-2h14z"
                                      />
                                    </svg>
                                  </span>
                    {patchLoading ? <Spinner /> : quantityState}
                    <span onClick={() => setQuantityState(quantityState + 1)}>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="1em"
                                        height="1em"
                                        viewBox="0 0 24 24">
                                      <path
                                          fill="currentColor"
                                          d="M19 12.998h-6v6h-2v-6H5v-2h6v-6h2v6h6z"
                                      />
                                    </svg>
                                  </span>
                </div>
                <p className="cart_item_price text-white mt-auto mb-2">
                    ${p1} {p2 && <span>.75</span>}
                </p>
            </div>
        </div>
    )
}